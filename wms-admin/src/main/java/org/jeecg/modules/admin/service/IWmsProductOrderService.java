package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsProductOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.admin.entity.WmsUseMaterialdetail;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 生产订单
 * @Author: jeecg-boot
 * @Date:   2024-10-26
 * @Version: V1.0
 */
public interface IWmsProductOrderService extends IService<WmsProductOrder> {

    void syncProductOrderData();

    /**
     * 添加一对多
     *
     * @param wmsProductOrder
     * @param wmsUseMaterialdetailList
     */
    public void saveMain(WmsProductOrder wmsProductOrder, List<WmsUseMaterialdetail> wmsUseMaterialdetailList) ;

    /**
     * 修改一对多
     *
     * @param wmsProductOrder
     * @param wmsUseMaterialdetailList
     */
    public void updateMain(WmsProductOrder wmsProductOrder,List<WmsUseMaterialdetail> wmsUseMaterialdetailList);

    /**
     * 删除一对多
     *
     * @param id
     */
    public void delMain (String id);

    /**
     * 批量删除一对多
     *
     * @param idList
     */
    public void delBatchMain (Collection<? extends Serializable> idList);

    void generateOfficialData(List<String> ids);

    /**
     * 查询最近7天的生产订单统计数据
     * @return 包含各状态订单数量和完成率的JSON对象
     */
    JSONObject getProductPlanStatus();
}
